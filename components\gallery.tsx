import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

export default function Gallery() {
  const projects = [
    {
      title: "International Medical Conference",
      category: "Conference",
      image: "/placeholder.svg?height=300&width=400",
      description: "500+ delegates, 50+ speakers",
    },
    {
      title: "Pharmaceutical Exhibition",
      category: "Exhibition",
      image: "/placeholder.svg?height=300&width=400",
      description: "200+ exhibitors, 10,000+ visitors",
    },
    {
      title: "Corporate Product Launch",
      category: "Corporate Event",
      image: "/placeholder.svg?height=300&width=400",
      description: "Global product launch event",
    },
    {
      title: "Healthcare Summit",
      category: "Conference",
      image: "/placeholder.svg?height=300&width=400",
      description: "International healthcare summit",
    },
    {
      title: "Technology Expo",
      category: "Exhibition",
      image: "/placeholder.svg?height=300&width=400",
      description: "Latest technology showcase",
    },
    {
      title: "Leadership Workshop",
      category: "Workshop",
      image: "/placeholder.svg?height=300&width=400",
      description: "Executive leadership program",
    },
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of successful events. From intimate corporate gatherings to large-scale international
            conferences, each project showcases our commitment to excellence.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  <Image
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    width={400}
                    height={300}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-gradient-to-r from-orange-500 to-rose-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {project.category}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{project.title}</h3>
                  <p className="text-gray-600">{project.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
