import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Calendar, Users, Award } from "lucide-react"

export default function Hero() {

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-orange-50 via-rose-50 to-amber-50 pt-16 sm:pt-24 md:pt-28 lg:pt-32 pb-8 sm:pb-20"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-amber-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10 mt-0">
        <div className="text-center space-y-8 animate-fade-in-up">
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-orange-600 border border-orange-200">
            <Award className="w-4 h-4" />
            Professional Conference Organizer
          </div>

          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold bg-gradient-to-r from-orange-600 via-rose-600 to-amber-600 bg-clip-text text-transparent leading-tight">
            WELCOME TO
            <span className="block">ORANGE ROSE</span>
          </h1>

          <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
            Your trusted partner for world-class conferences, exhibitions, and corporate events. We transform your
            vision into unforgettable experiences with precision and excellence.
          </p>

          {/* Service highlights */}
          <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8 max-w-6xl mx-auto mt-8 sm:mt-16 px-4">
            {[
              {
                icon: Users,
                title: "CONFERENCE",
                desc: "Conference Management, Advisory Board Meetings, Investigator meetings, MICE Programme, CME",
                delay: "0",
              },
              {
                icon: Calendar,
                title: "EXHIBITION",
                desc: "Exhibition Management, Sales and Marketing, Operation Management, Visitor Promotion (National and International)",
                delay: "200",
              },
              {
                icon: Award,
                title: "CORPORATE EVENTS",
                desc: "International Speaker Program, Product launch, Live Workshops",
                delay: "400",
              },
            ].map((service, index) => (
              <div
                key={index}
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 animate-fade-in-up"
                style={{ animationDelay: `${service.delay}ms` }}
              >
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300">
                  <service.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                </div>
                <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">{service.title}</h3>
                <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">{service.desc}</p>
              </div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mt-6 sm:mt-8 px-4">
            <Button
              size="lg"
              className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <a href="#contact">Contact Us Today</a>
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-orange-300 text-orange-600 hover:bg-orange-50 px-8 py-3 rounded-full transition-all duration-300 bg-transparent"
            >
              <a href="#work">View Our Work</a>
            </Button>
          </div>


        </div>
      </div>
    </section>
  )
}
