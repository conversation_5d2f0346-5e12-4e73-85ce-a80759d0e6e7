"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"
import { Globe, Star, TrendingUp, Award } from "lucide-react"

export default function Clients() {
  const scrollRef = useRef<HTMLDivElement>(null)

  const clients = [
    { name: "<PERSON><PERSON><PERSON>", logo: "/placeholder.svg?height=80&width=160&text=Pfizer" },
    { name: "<PERSON> & Johnson", logo: "/placeholder.svg?height=80&width=160&text=J&J" },
    { name: "Novartis", logo: "/placeholder.svg?height=80&width=160&text=Novartis" },
    { name: "<PERSON>", logo: "/placeholder.svg?height=80&width=160&text=Roche" },
    { name: "<PERSON><PERSON><PERSON>", logo: "/placeholder.svg?height=80&width=160&text=GSK" },
    { name: "<PERSON><PERSON><PERSON>", logo: "/placeholder.svg?height=80&width=160&text=Merck" },
    { name: "AstraZeneca", logo: "/placeholder.svg?height=80&width=160&text=AstraZeneca" },
    { name: "<PERSON>of<PERSON>", logo: "/placeholder.svg?height=80&width=160&text=Sanofi" },
    { name: "<PERSON>", logo: "/placeholder.svg?height=80&width=160&text=Abbott" },
    { name: "Bayer", logo: "/placeholder.svg?height=80&width=160&text=Bayer" },
    { name: "Bristol Myers", logo: "/placeholder.svg?height=80&width=160&text=BMS" },
    { name: "Eli Lilly", logo: "/placeholder.svg?height=80&width=160&text=Lilly" },
  ]

  useEffect(() => {
    const scrollContainer = scrollRef.current
    if (!scrollContainer) return

    const scroll = () => {
      if (scrollContainer.scrollLeft >= scrollContainer.scrollWidth / 2) {
        scrollContainer.scrollLeft = 0
      } else {
        scrollContainer.scrollLeft += 1
      }
    }

    const interval = setInterval(scroll, 30)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 to-orange-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 sm:mb-16">
          <div className="inline-flex items-center gap-2 bg-orange-100 rounded-full px-6 py-2 text-sm font-medium text-orange-600 mb-6">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Trusted Partners
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Clients</span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We are proud to work with leading organizations across healthcare, pharmaceuticals, and technology sectors,
            delivering exceptional events that drive their business objectives.
          </p>
        </div>

        {/* Client Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-12 sm:mb-16">
          {[
            { number: "200+", label: "Global Clients", icon: Globe },
            { number: "15+", label: "Years Experience", icon: Star },
            { number: "50+", label: "Countries Served", icon: TrendingUp },
            { number: "98%", label: "Client Retention", icon: Award },
          ].map((stat, index) => (
            <div
              key={index}
              className="text-center p-4 sm:p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl sm:text-3xl font-bold text-orange-600 mb-1">{stat.number}</div>
              <div className="text-sm sm:text-base text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Logo Carousel */}
        <div className="relative overflow-hidden bg-white/80 backdrop-blur-sm rounded-3xl p-6 sm:p-8 shadow-lg">
          <h3 className="text-xl sm:text-2xl font-bold text-center text-gray-900 mb-8">Trusted by Industry Leaders</h3>

          <div className="relative overflow-hidden">
            <div
              ref={scrollRef}
              className="flex gap-8 sm:gap-12 overflow-hidden"
              style={{
                width: "200%",
                animation: "scroll 40s linear infinite",
              }}
            >
              {/* First set of logos */}
              {clients.map((client, index) => (
                <div
                  key={`first-${index}`}
                  className="flex-shrink-0 w-32 sm:w-40 h-16 sm:h-20 flex items-center justify-center bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 grayscale hover:grayscale-0"
                >
                  <Image
                    src={client.logo || "/placeholder.svg"}
                    alt={`${client.name} logo`}
                    width={160}
                    height={80}
                    className="max-w-full max-h-full object-contain p-2 sm:p-3"
                  />
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {clients.map((client, index) => (
                <div
                  key={`second-${index}`}
                  className="flex-shrink-0 w-32 sm:w-40 h-16 sm:h-20 flex items-center justify-center bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 grayscale hover:grayscale-0"
                >
                  <Image
                    src={client.logo || "/placeholder.svg"}
                    alt={`${client.name} logo`}
                    width={160}
                    height={80}
                    className="max-w-full max-h-full object-contain p-2 sm:p-3"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Client Success Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mt-12 sm:mt-16">
          {[
            { metric: "95%", label: "Client Retention Rate" },
            { metric: "300+", label: "Average Event Size" },
            { metric: "40+", label: "Countries Served" },
            { metric: "1000+", label: "Successful Events" },
          ].map((item, index) => (
            <div
              key={index}
              className="text-center p-4 sm:p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="text-2xl sm:text-3xl font-bold text-orange-600 mb-1">{item.metric}</div>
              <div className="text-sm sm:text-base text-gray-600">{item.label}</div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12 sm:mt-16">
          <p className="text-lg text-gray-600 mb-6">
            Join our growing list of satisfied clients and let us make your next event extraordinary.
          </p>
          <button className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold">
            Become Our Client
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
      `}</style>
    </section>
  )
}
