import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { MapPin, Phone, Mail, Clock } from "lucide-react"

export default function Contact() {
  const contactInfo = [
    {
      icon: Phone,
      title: "Call Us",
      details: ["+91 8000622067", "Available during business hours"],
    },
    {
      icon: Mail,
      title: "Email Us",
      details: ["<EMAIL>", "Quick response guaranteed"],
    },
    {
      icon: Clock,
      title: "Working Hours",
      details: ["Monday - Friday: 10am - 6pm", "Saturday: 10am - 4pm"],
    },
    {
      icon: MapPin,
      title: "Visit Us",
      details: ["Professional Event Management", "Serving clients nationwide"],
    },
  ]

  return (
    <section id="contact" className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Get In{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Touch</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Ready to create an unforgettable event? Contact us today to discuss your requirements and let us bring your
            vision to life with our professional event management services.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          <div>
            <div className="grid sm:grid-cols-2 gap-6 mb-8">
              {contactInfo.map((info, index) => (
                <Card
                  key={index}
                  className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-orange-50 to-rose-50 border-0"
                >
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <info.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">{info.title}</h3>
                    {info.details.map((detail, i) => (
                      <p key={i} className="text-gray-600 text-sm">
                        {detail}
                      </p>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <Card className="bg-white shadow-2xl border-0 rounded-3xl overflow-hidden">
            <CardContent className="p-10">
              <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">Let's Plan Your Event</h3>
              <form className="space-y-6">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                    <Input
                      placeholder="Your full name"
                      className="border-gray-200 focus:border-orange-400 focus:ring-orange-400"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                    <Input
                      placeholder="Your company name"
                      className="border-gray-200 focus:border-orange-400 focus:ring-orange-400"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="border-gray-200 focus:border-orange-400 focus:ring-orange-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  <Input
                    type="tel"
                    placeholder="Your phone number"
                    className="border-gray-200 focus:border-orange-400 focus:ring-orange-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Event Type</label>
                  <select className="w-full px-3 py-2 border border-gray-200 rounded-md focus:border-orange-400 focus:ring-orange-400">
                    <option>Select event type</option>
                    <option>Conference</option>
                    <option>Exhibition</option>
                    <option>Corporate Event</option>
                    <option>Workshop</option>
                    <option>Product Launch</option>
                    <option>Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Event Details</label>
                  <Textarea
                    placeholder="Tell us about your event requirements, expected attendees, dates, and any specific needs..."
                    rows={4}
                    className="border-gray-200 focus:border-orange-400 focus:ring-orange-400"
                  />
                </div>
                <Button className="w-full bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white py-3 rounded-full transition-all duration-300 transform hover:scale-105">
                  Send Inquiry
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
