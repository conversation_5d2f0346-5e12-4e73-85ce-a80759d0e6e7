import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { Users, Target, Heart, Lightbulb, Shield, Award, Globe, Clock } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"

export default function AboutPage() {
  const values = [
    {
      icon: Target,
      title: "Excellence",
      description:
        "We strive for perfection in every event we organize, ensuring exceptional quality and attention to detail in all aspects of event management.",
    },
    {
      icon: Heart,
      title: "Passion",
      description:
        "Our team is passionate about creating memorable experiences that leave lasting impressions on attendees and exceed client expectations.",
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description:
        "We embrace cutting-edge technology and creative solutions to deliver unique, engaging, and impactful events that stand out.",
    },
    {
      icon: Shield,
      title: "Reliability",
      description:
        "Our clients trust us to deliver consistent, professional results on time and within budget, every single time.",
    },
  ]

  const team = [
    {
      name: "<PERSON><PERSON>",
      position: "Founder & CEO",
      image: "/placeholder.svg?height=400&width=400",
      bio: "With over 15 years of experience in event management, <PERSON><PERSON> leads OrangeRose with vision and expertise, having successfully managed over 200 major events.",
      expertise: ["Strategic Planning", "Client Relations", "Team Leadership"],
    },
    {
      name: "<PERSON><PERSON>",
      position: "Operations Director",
      image: "/placeholder.svg?height=400&width=400",
      bio: "Priya ensures seamless execution of all our events with her exceptional organizational skills and attention to detail in operations management.",
      expertise: ["Operations Management", "Vendor Coordination", "Quality Control"],
    },
    {
      name: "Amit Patel",
      position: "Creative Director",
      image: "/placeholder.svg?height=400&width=400",
      bio: "Amit brings creative vision to life, designing memorable experiences and innovative concepts for every event we manage.",
      expertise: ["Creative Design", "Brand Strategy", "Visual Communications"],
    },
    {
      name: "Sunita Reddy",
      position: "Client Relations Manager",
      image: "/placeholder.svg?height=400&width=400",
      bio: "Sunita maintains strong relationships with our clients, ensuring their needs are always met and expectations exceeded.",
      expertise: ["Client Management", "Communication", "Relationship Building"],
    },
  ]

  const milestones = [
    { year: "2009", event: "OrangeRose Founded", description: "Started with a vision to transform event management" },
    {
      year: "2012",
      event: "First International Event",
      description: "Successfully managed our first international conference",
    },
    { year: "2015", event: "100+ Events Milestone", description: "Crossed the milestone of 100 successful events" },
    { year: "2018", event: "Pan-India Expansion", description: "Expanded operations to major cities across India" },
    { year: "2020", event: "Virtual Events Pioneer", description: "Led the transition to hybrid and virtual events" },
    { year: "2024", event: "500+ Events Achievement", description: "Celebrating over 500 successful events managed" },
  ]

  const stats = [
    { number: "500+", label: "Events Managed", icon: Award },
    { number: "100K+", label: "Attendees Served", icon: Users },
    { number: "50+", label: "Cities Covered", icon: Globe },
    { number: "15+", label: "Years Experience", icon: Clock },
  ]

  return (
    <main className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="relative pt-40 pb-24 bg-gradient-to-br from-orange-50 via-rose-50 to-amber-50 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-orange-600 border border-orange-200 mb-8">
              <Users className="w-4 h-4" />
              About OrangeRose
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Crafting Extraordinary{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Experiences
              </span>
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12">
              Since 2009, OrangeRose has been at the forefront of event management, transforming visions into reality
              through innovative solutions, meticulous planning, and flawless execution.
            </p>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                  <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                  <div className="text-gray-600 font-medium text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 lg:gap-24 items-center mb-24">
            <div className="order-2 lg:order-1">
              <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">Our Story</h2>
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  OrangeRose was born from a simple yet powerful vision: to revolutionize the event management industry
                  by creating experiences that inspire, connect, and transform. What started as a small team with big
                  dreams has evolved into one of India's most trusted event management companies.
                </p>
                <p>
                  Our journey began in 2009 when our founder, Rajesh Kumar, recognized the need for professional,
                  innovative event management services in the rapidly growing Indian market. From our first corporate
                  seminar to managing international conferences with thousands of delegates, we have consistently pushed
                  the boundaries of what's possible in event management.
                </p>
                <p>
                  Today, we proudly serve clients across healthcare, pharmaceuticals, technology, finance, and
                  government sectors, having successfully managed over 500 events that have connected more than 100,000
                  professionals worldwide.
                </p>
              </div>
            </div>
            <div className="order-1 lg:order-2 relative">
              <div className="aspect-square bg-gradient-to-br from-orange-200 to-rose-200 rounded-3xl overflow-hidden">
                <Image
                  src="/images/about/about.jpg?height=600&width=600"
                  alt="OrangeRose Story"
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -bottom-8 -right-8 bg-white rounded-2xl p-6 shadow-2xl">
                <div className="flex items-center gap-4">
                  <Award className="w-10 h-10 text-orange-600" />
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Industry Leader</div>
                    <div className="text-gray-600">Since 2009</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="mb-24">
            <h3 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">Our Journey</h3>
            <div className="relative max-w-6xl mx-auto">
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-orange-500 to-rose-500 rounded-full hidden lg:block"></div>
              <div className="space-y-12">
                {milestones.map((milestone, index) => (
                  <div
                    key={index}
                    className={`flex items-center ${index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"}`}
                  >
                    <div className={`w-full lg:w-1/2 ${index % 2 === 0 ? "lg:pr-12" : "lg:pl-12"}`}>
                      <Card className="bg-white shadow-xl border-0 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <CardContent className="p-8">
                          <div className="text-3xl font-bold text-orange-600 mb-3">{milestone.year}</div>
                          <h4 className="text-xl font-semibold text-gray-900 mb-3">{milestone.event}</h4>
                          <p className="text-gray-600 leading-relaxed">{milestone.description}</p>
                        </CardContent>
                      </Card>
                    </div>
                    <div className="relative z-10 hidden lg:block">
                      <div className="w-6 h-6 bg-white border-4 border-orange-500 rounded-full"></div>
                    </div>
                    <div className="w-full lg:w-1/2 hidden lg:block"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Our Core{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Values</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              These fundamental principles guide every decision we make and every event we create, ensuring we deliver
              exceptional value to our clients and their audiences.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card
                key={index}
                className="text-center group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white/80 backdrop-blur-sm border-0"
              >
                <CardContent className="p-8 lg:p-10">
                  <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-8 group-hover:scale-110 transition-transform duration-300">
                    <value.icon className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Meet Our{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Leadership Team
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our experienced leadership team brings together decades of expertise in event management, operations,
              creative design, and client relations to deliver exceptional results.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-white border-0 overflow-hidden"
              >
                <CardContent className="p-0">
                  <div className="relative overflow-hidden">
                    <Image
                      src={member.image || "/placeholder.svg"}
                      alt={member.name}
                      width={400}
                      height={400}
                      className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{member.name}</h3>
                    <p className="text-orange-600 font-medium mb-4">{member.position}</p>
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">{member.bio}</p>
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-gray-900">Expertise:</div>
                      <div className="flex flex-wrap gap-2">
                        {member.expertise.map((skill, i) => (
                          <span key={i} className="bg-orange-100 text-orange-700 text-xs px-3 py-1 rounded-full">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-24 lg:py-32 bg-gradient-to-r from-orange-500 to-rose-500">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 text-white">
            <div className="text-center lg:text-left">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-8 mx-auto lg:mx-0">
                <Target className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl lg:text-4xl font-bold mb-8">Our Mission</h2>
              <p className="text-xl leading-relaxed opacity-90 mb-8">
                To transform ideas into extraordinary experiences by delivering innovative, professional, and memorable
                events that connect people, inspire innovation, and drive business success for our clients.
              </p>
              <p className="text-lg leading-relaxed opacity-80">
                We are committed to excellence in every aspect of event management, from initial concept to final
                execution, ensuring each event exceeds expectations and creates lasting value.
              </p>
            </div>
            <div className="text-center lg:text-left">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-8 mx-auto lg:mx-0">
                <Globe className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-3xl lg:text-4xl font-bold mb-8">Our Vision</h2>
              <p className="text-xl leading-relaxed opacity-90 mb-8">
                To be the most trusted and innovative event management company globally, recognized for our creativity,
                professionalism, and ability to create transformative experiences that make a lasting impact.
              </p>
              <p className="text-lg leading-relaxed opacity-80">
                We envision a future where every event we manage becomes a catalyst for meaningful connections,
                knowledge sharing, and positive change in the industries we serve.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Why Choose{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                OrangeRose?
              </span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Award,
                title: "Proven Track Record",
                description: "Over 500 successful events across multiple industries with 98% client satisfaction rate.",
              },
              {
                icon: Users,
                title: "Expert Team",
                description:
                  "Experienced professionals with specialized expertise in various aspects of event management.",
              },
              {
                icon: Globe,
                title: "Pan-India Presence",
                description: "Operations across major Indian cities with capability to manage international events.",
              },
              {
                icon: Lightbulb,
                title: "Innovative Solutions",
                description: "Cutting-edge technology and creative approaches to deliver unique event experiences.",
              },
              {
                icon: Shield,
                title: "Reliable Partnership",
                description: "Consistent delivery, transparent communication, and long-term client relationships.",
              },
              {
                icon: Clock,
                title: "Timely Execution",
                description: "Meticulous planning and project management ensuring on-time, on-budget delivery.",
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className="text-center group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 bg-gradient-to-br from-orange-50 to-rose-50 border-0"
              >
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
            Ready to Create Something Extraordinary?
          </h2>
          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed">
            Let's discuss how we can bring your vision to life and create an unforgettable event experience.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold text-lg"
            >
              Start Your Project
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-orange-300 text-orange-600 hover:bg-orange-50 px-10 py-4 rounded-full transition-all duration-300 bg-transparent font-semibold text-lg"
            >
              Schedule Consultation
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
