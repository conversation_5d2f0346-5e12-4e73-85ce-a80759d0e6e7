import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Calendar, MapPin, ExternalLink, Filter, Users, Award, Globe, Star } from "lucide-react"
import Image from "next/image"

export default function ProjectsPage() {
  const featuredProjects = [
    {
      id: 1,
      title: "International Medical Conference 2024",
      category: "Conference",
      image: "/placeholder.svg?height=400&width=600",
      description:
        "A comprehensive medical conference featuring leading healthcare professionals from around the world, focusing on latest medical innovations, research breakthroughs, and clinical practices with international participation.",
      stats: { attendees: "500+", speakers: "50+", days: "3", countries: "15" },
      location: "Mumbai, India",
      date: "March 15-17, 2024",
      client: "Global Health Alliance",
      budget: "₹45 Lakhs",
      highlights: [
        "International speaker lineup from 15 countries",
        "Live surgical demonstrations with 4K streaming",
        "Medical equipment exhibition with 80+ exhibitors",
        "Networking sessions and gala dinner",
        "CME accreditation for 500+ delegates",
      ],
      testimonial:
        "OrangeRose delivered an exceptional conference that exceeded all our expectations. The attention to detail and professional execution was remarkable.",
      clientName: "Dr. <PERSON>, Conference Chair",
      color: "from-blue-500 to-blue-600",
    },
    {
      id: 2,
      title: "Pharmaceutical Innovation Expo 2024",
      category: "Exhibition",
      image: "/placeholder.svg?height=400&width=600",
      description:
        "India's largest pharmaceutical exhibition showcasing cutting-edge innovations, research developments, and emerging technologies in the pharmaceutical industry with global participation.",
      stats: { exhibitors: "200+", visitors: "10,000+", days: "4", countries: "25" },
      location: "Delhi, India",
      date: "February 20-23, 2024",
      client: "Pharma Industry Association",
      budget: "₹75 Lakhs",
      highlights: [
        "200+ exhibitors from pharmaceutical industry",
        "Product launches and technology demonstrations",
        "B2B networking with 10,000+ visitors",
        "Regulatory updates and compliance sessions",
        "Innovation awards and recognition ceremony",
      ],
      testimonial:
        "The exhibition was a tremendous success. OrangeRose managed every aspect professionally, resulting in our highest ROI to date.",
      clientName: "Michael Chen, Director of Operations",
      color: "from-green-500 to-green-600",
    },
    {
      id: 3,
      title: "Global Tech Summit 2024",
      category: "Corporate Event",
      image: "/placeholder.svg?height=400&width=600",
      description:
        "Annual technology summit bringing together industry leaders, innovators, and tech enthusiasts to discuss future trends, emerging technologies, and digital transformation initiatives.",
      stats: { attendees: "800+", speakers: "30+", days: "2", startups: "50" },
      location: "Bangalore, India",
      date: "January 25-26, 2024",
      client: "TechCorp Solutions",
      budget: "₹35 Lakhs",
      highlights: [
        "Keynote presentations from tech industry leaders",
        "Startup pitch sessions with 50+ startups",
        "Technology exhibitions and product demos",
        "Innovation awards and recognition",
        "Networking sessions and tech talks",
      ],
      testimonial:
        "An outstanding event that perfectly captured our vision. The team's creativity and execution were phenomenal.",
      clientName: "Emily Rodriguez, VP Marketing",
      color: "from-purple-500 to-purple-600",
    },
  ]

  const allProjects = [
    {
      id: 4,
      title: "Healthcare Leadership Workshop",
      category: "Workshop",
      image: "/placeholder.svg?height=300&width=400",
      description: "Intensive leadership development program for healthcare executives and senior management.",
      stats: { participants: "100+", sessions: "12", days: "2" },
      location: "Chennai, India",
      date: "December 2023",
      client: "Healthcare Leaders Forum",
      color: "from-orange-500 to-rose-500",
    },
    {
      id: 5,
      title: "Digital Health Conference",
      category: "Conference",
      image: "/placeholder.svg?height=300&width=400",
      description: "Exploring the future of digital health technologies, telemedicine, and healthcare innovation.",
      stats: { attendees: "600+", speakers: "40+", days: "3" },
      location: "Pune, India",
      date: "November 2023",
      client: "Digital Health Initiative",
      color: "from-blue-500 to-blue-600",
    },
    {
      id: 6,
      title: "Biotech Innovation Showcase",
      category: "Exhibition",
      image: "/placeholder.svg?height=300&width=400",
      description: "Premier biotechnology exhibition featuring cutting-edge research and innovative solutions.",
      stats: { exhibitors: "150+", visitors: "8,000+", days: "3" },
      location: "Hyderabad, India",
      date: "October 2023",
      client: "Biotech Association",
      color: "from-green-500 to-green-600",
    },
    {
      id: 7,
      title: "Financial Services Summit",
      category: "Corporate Event",
      image: "/placeholder.svg?height=300&width=400",
      description: "Annual summit for financial services industry leaders and banking professionals.",
      stats: { attendees: "400+", speakers: "25+", days: "2" },
      location: "Mumbai, India",
      date: "September 2023",
      client: "Finance Corp",
      color: "from-purple-500 to-purple-600",
    },
    {
      id: 8,
      title: "Education Technology Expo",
      category: "Exhibition",
      image: "/placeholder.svg?height=300&width=400",
      description: "Showcasing latest innovations in educational technology and e-learning solutions.",
      stats: { exhibitors: "120+", visitors: "6,000+", days: "3" },
      location: "Delhi, India",
      date: "August 2023",
      client: "EdTech Alliance",
      color: "from-indigo-500 to-indigo-600",
    },
    {
      id: 9,
      title: "Manufacturing Excellence Conference",
      category: "Conference",
      image: "/placeholder.svg?height=300&width=400",
      description: "Conference focused on manufacturing best practices, innovation, and industry 4.0.",
      stats: { attendees: "350+", speakers: "20+", days: "2" },
      location: "Bangalore, India",
      date: "July 2023",
      client: "Manufacturing Association",
      color: "from-pink-500 to-pink-600",
    },
  ]

  const categories = ["All", "Conference", "Exhibition", "Corporate Event", "Workshop"]

  const stats = [
    { number: "500+", label: "Projects Completed", icon: Award },
    { number: "100K+", label: "Total Attendees", icon: Users },
    { number: "50+", label: "Countries Reached", icon: Globe },
    { number: "98%", label: "Success Rate", icon: Star },
  ]

  return (
    <main className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="relative pt-40 pb-24 bg-gradient-to-br from-orange-50 via-rose-50 to-amber-50 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-orange-600 border border-orange-200 mb-8 animate-fade-in-up">
              <Calendar className="w-4 h-4" />
              Our Projects
            </div>
            <h1
              className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-8 leading-tight animate-fade-in-up"
              style={{ animationDelay: "200ms" }}
            >
              Featured{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Projects
              </span>
            </h1>
            <p
              className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-12 animate-fade-in-up"
              style={{ animationDelay: "400ms" }}
            >
              Explore our portfolio of successful events that have brought together industry leaders, innovators, and
              professionals from around the world. Each project showcases our commitment to excellence and innovation.
            </p>

            {/* Project Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg animate-fade-in-up"
                  style={{ animationDelay: `${600 + index * 100}ms` }}
                >
                  <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                  <div className="text-3xl font-bold text-orange-600 mb-2">{stat.number}</div>
                  <div className="text-gray-600 font-medium text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-24 lg:py-32 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              Featured{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Success Stories
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Detailed case studies of our most impactful and successful events, showcasing our expertise and measurable
              results.
            </p>
          </div>

          <div className="space-y-24">
            {featuredProjects.map((project, index) => (
              <div
                key={project.id}
                className={`grid lg:grid-cols-2 gap-12 lg:gap-16 items-center ${index % 2 === 1 ? "lg:grid-flow-col-dense" : ""}`}
              >
                <div className={index % 2 === 1 ? "lg:col-start-2" : ""}>
                  <Card className="overflow-hidden border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2">
                    <CardContent className="p-0">
                      <div className="relative overflow-hidden">
                        <Image
                          src={project.image || "/placeholder.svg"}
                          alt={project.title}
                          width={600}
                          height={400}
                          className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div
                          className={`absolute top-4 left-4 bg-gradient-to-r ${project.color} text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg`}
                        >
                          {project.category}
                        </div>
                        <div className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                          {project.budget}
                        </div>
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className={index % 2 === 1 ? "lg:col-start-1" : ""}>
                  <div className="space-y-6">
                    <div>
                      <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          {project.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {project.date}
                        </div>
                      </div>

                      <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">{project.title}</h3>
                      <p className="text-lg text-gray-600 leading-relaxed mb-6">{project.description}</p>

                      <div className="text-sm text-orange-600 font-medium mb-6">Client: {project.client}</div>
                    </div>

                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                      {Object.entries(project.stats).map(([key, value]) => (
                        <div
                          key={key}
                          className="text-center bg-gradient-to-r from-orange-50 to-rose-50 rounded-xl p-4 hover:shadow-md transition-all duration-300"
                        >
                          <div className="text-2xl font-bold text-orange-600">{value}</div>
                          <div className="text-xs text-gray-600 capitalize">{key}</div>
                        </div>
                      ))}
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-4">Key Highlights:</h4>
                      <ul className="space-y-3">
                        {project.highlights.map((highlight, i) => (
                          <li key={i} className="flex items-start text-gray-600">
                            <div className="w-2 h-2 bg-orange-400 rounded-full mr-4 mt-2 flex-shrink-0"></div>
                            {highlight}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-gradient-to-r from-orange-50 to-rose-50 rounded-xl p-6 border-l-4 border-orange-500">
                      <blockquote className="text-gray-700 italic mb-3 text-lg">"{project.testimonial}"</blockquote>
                      <cite className="text-orange-600 font-medium">— {project.clientName}</cite>
                    </div>

                    <Button className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white rounded-full px-8 py-3 transition-all duration-300 transform hover:scale-105 shadow-lg">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Full Case Study
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* All Projects Grid */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-gray-50 to-orange-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8">
              More{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Projects
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
              Browse through our extensive portfolio of successful events across various industries and formats.
            </p>

            {/* Filter Buttons */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category, index) => (
                <Button
                  key={index}
                  variant={index === 0 ? "default" : "outline"}
                  className={`rounded-full px-6 py-2 transition-all duration-300 ${
                    index === 0
                      ? "bg-gradient-to-r from-orange-500 to-rose-500 text-white hover:from-orange-600 hover:to-rose-600"
                      : "border-orange-300 text-orange-600 hover:bg-orange-50"
                  }`}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {allProjects.map((project, index) => (
              <Card
                key={project.id}
                className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 overflow-hidden bg-white border-0"
              >
                <CardContent className="p-0">
                  <div className="relative overflow-hidden">
                    <Image
                      src={project.image || "/placeholder.svg"}
                      alt={project.title}
                      width={400}
                      height={300}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div
                      className={`absolute top-4 left-4 bg-gradient-to-r ${project.color} text-white px-3 py-1 rounded-full text-sm font-semibold`}
                    >
                      {project.category}
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Button
                      size="sm"
                      className="absolute bottom-4 right-4 bg-white/90 text-gray-900 hover:bg-white opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {project.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {project.date}
                      </div>
                    </div>

                    <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors duration-300">
                      {project.title}
                    </h3>
                    <p className="text-gray-600 mb-4 leading-relaxed text-sm">{project.description}</p>

                    <div className="grid grid-cols-3 gap-2 pt-4 border-t border-gray-100">
                      {Object.entries(project.stats).map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className="text-sm font-bold text-orange-600">{value}</div>
                          <div className="text-xs text-gray-500 capitalize">{key}</div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <div className="text-sm text-gray-500">
                        <span className="font-medium">Client:</span> {project.client}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More Button */}
          <div className="text-center mt-12">
            <Button
              size="lg"
              className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
            >
              Load More Projects
              <ExternalLink className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 lg:py-32 bg-gradient-to-r from-orange-500 to-rose-500">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-5xl font-bold text-white mb-8">Ready to Create Your Success Story?</h2>
          <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed">
            Let's collaborate to create an exceptional event that achieves your objectives and leaves a lasting impact
            on your audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-white text-orange-600 hover:bg-gray-100 px-10 py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold text-lg"
            >
              Start Your Project
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-10 py-4 rounded-full transition-all duration-300 bg-transparent font-semibold text-lg"
            >
              Schedule Consultation
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
