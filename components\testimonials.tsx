"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Star, Quote, ChevronLeft, ChevronRight, Calendar, Users } from "lucide-react"

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const testimonials = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      position: "Chief Medical Officer",
      company: "Global Health Solutions",
      rating: 5,
      text: "OrangeRose transformed our annual medical conference into an extraordinary experience. Their attention to detail and professional execution exceeded all our expectations. The seamless coordination of international speakers and flawless technical support made our event truly memorable.",
      event: "International Medical Conference 2024",
      attendees: "500+",
      category: "Healthcare",
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Director of Operations",
      company: "PharmaTech Industries",
      rating: 5,
      text: "Working with OrangeRose for our pharmaceutical exhibition was a game-changer. They managed every aspect professionally, from vendor coordination to visitor engagement. The result was our most successful exhibition to date with record attendance and lead generation.",
      event: "Pharmaceutical Innovation Expo 2024",
      attendees: "10,000+",
      category: "Pharmaceutical",
    },
    {
      id: 3,
      name: "<PERSON> Rodriguez",
      position: "VP Marketing",
      company: "TechCorp Solutions",
      rating: 5,
      text: "The corporate event organized by OrangeRose was absolutely phenomenal. From the initial planning stages to the final execution, their team demonstrated exceptional creativity and professionalism. Our product launch event was a resounding success.",
      event: "Global Tech Summit 2024",
      attendees: "800+",
      category: "Technology",
    },
    {
      id: 4,
      name: "Dr. Rajesh Patel",
      position: "Research Director",
      company: "BioMed Research Institute",
      rating: 5,
      text: "OrangeRose's expertise in managing scientific conferences is unparalleled. They understood our unique requirements and delivered an event that facilitated meaningful connections between researchers worldwide. Highly recommended for academic events.",
      event: "Biomedical Research Conference 2024",
      attendees: "600+",
      category: "Research",
    },
    {
      id: 5,
      name: "Lisa Thompson",
      position: "Event Manager",
      company: "Healthcare Alliance",
      rating: 5,
      text: "The level of service and attention to detail provided by OrangeRose is exceptional. They handled our multi-day healthcare summit with such precision that we could focus entirely on content while they managed all logistics seamlessly.",
      event: "Healthcare Leadership Summit 2024",
      attendees: "400+",
      category: "Healthcare",
    },
    {
      id: 6,
      name: "James Wilson",
      position: "CEO",
      company: "Innovation Labs",
      rating: 5,
      text: "OrangeRose exceeded our expectations in every way. Their innovative approach to event management and their ability to adapt to our changing requirements made them the perfect partner for our corporate events. We look forward to working with them again.",
      event: "Innovation Showcase 2024",
      attendees: "300+",
      category: "Corporate",
    },
  ]

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
    }, 6000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, testimonials.length])

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-rose-50 to-orange-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 sm:mb-16">
          <div className="inline-flex items-center gap-2 bg-rose-100 rounded-full px-6 py-2 text-sm font-medium text-rose-600 mb-6">
            <Quote className="w-4 h-4" />
            Client Testimonials
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            What Our{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
              Clients Say
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Real experiences from real clients who have trusted us with their most important events. Their words speak
            volumes about our commitment to excellence.
          </p>
        </div>

        {/* Featured Testimonial */}
        <div className="relative max-w-5xl mx-auto mb-12">
          <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-2xl overflow-hidden">
            <CardContent className="p-8 sm:p-12 text-center">
              <Quote className="w-16 h-16 text-orange-500 mx-auto mb-8 opacity-20" />

              <blockquote className="text-xl sm:text-2xl lg:text-3xl text-gray-700 leading-relaxed italic mb-8 font-light">
                "{testimonials[currentIndex].text}"
              </blockquote>

              <div className="flex justify-center mb-6">
                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 fill-orange-400 text-orange-400" />
                ))}
              </div>

              <div className="space-y-2">
                <h4 className="text-xl font-bold text-gray-900">{testimonials[currentIndex].name}</h4>
                <p className="text-orange-600 font-semibold">{testimonials[currentIndex].position}</p>
                <p className="text-gray-600">{testimonials[currentIndex].company}</p>
              </div>

              <div className="flex items-center justify-center gap-6 mt-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>{testimonials[currentIndex].event}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>{testimonials[currentIndex].attendees} Attendees</span>
                </div>
              </div>

              <div className="mt-4">
                <span className="inline-block bg-orange-100 text-orange-700 px-4 py-2 rounded-full text-sm font-medium">
                  {testimonials[currentIndex].category}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Arrows */}
          <Button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110"
            size="sm"
          >
            <ChevronLeft className="w-5 h-5" />
          </Button>
          <Button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110"
            size="sm"
          >
            <ChevronRight className="w-5 h-5" />
          </Button>
        </div>

        {/* Testimonial Dots */}
        <div className="flex justify-center gap-2 mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? "bg-gradient-to-r from-orange-500 to-rose-500 scale-125"
                  : "bg-gray-300 hover:bg-gray-400"
              }`}
            />
          ))}
        </div>

        {/* Text-Only Testimonial Grid */}
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className="group hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm border-0 cursor-pointer"
              onClick={() => goToTestimonial(index)}
            >
              <CardContent className="p-6 text-center">
                <Quote className="w-8 h-8 text-orange-500 mx-auto mb-4 opacity-30" />

                <div className="flex justify-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-orange-400 text-orange-400" />
                  ))}
                </div>

                <blockquote className="text-gray-700 text-sm leading-relaxed italic mb-6 line-clamp-4">
                  "{testimonial.text}"
                </blockquote>

                <div className="space-y-1">
                  <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                  <p className="text-orange-600 text-sm font-medium">{testimonial.position}</p>
                  <p className="text-gray-600 text-sm">{testimonial.company}</p>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs">
                    {testimonial.category}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-orange-500 to-rose-500 rounded-3xl p-8 sm:p-12 text-white">
            <Quote className="w-12 h-12 text-white/30 mx-auto mb-6" />
            <h3 className="text-2xl sm:text-3xl font-bold mb-4">Ready to Share Your Success Story?</h3>
            <p className="text-lg sm:text-xl mb-6 opacity-90 max-w-2xl mx-auto">
              Join our growing list of satisfied clients and let us create an exceptional event experience for you.
            </p>
            <Button
              size="lg"
              className="bg-white text-orange-600 hover:bg-gray-100 px-8 sm:px-10 py-3 sm:py-4 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
            >
              Start Your Project
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
