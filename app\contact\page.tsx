"use client"

import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { MapPin, Phone, Mail, Clock, Send, MessageCircle, Globe, Award } from "lucide-react"

export default function ContactPage() {
  const contactInfo = [
    {
      icon: Phone,
      title: "Call Us",
      details: ["+91 8000622067", "Available during business hours"],
      action: "tel:+918000622067",
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: Mail,
      title: "Email Us",
      details: ["<EMAIL>", "Quick response guaranteed"],
      action: "mailto:<EMAIL>",
      color: "from-green-500 to-green-600",
    },
    {
      icon: Clock,
      title: "Working Hours",
      details: ["Monday - Friday: 10am - 6pm", "Saturday: 10am - 4pm"],
      action: null,
      color: "from-purple-500 to-purple-600",
    },
    {
      icon: MapPin,
      title: "Visit Us",
      details: ["Professional Event Management", "Serving clients nationwide"],
      action: null,
      color: "from-orange-500 to-rose-500",
    },
  ]

  const offices = [
    {
      city: "Ahmedabad",
      address: "B-5, First Floor, B. Jadav Chambers, Ashram Rd, above Sales India, Mill Officer's Colony, Navrangpura, Ahmedabad, Gujarat 380009",
      phone: "+91 8000622067",
      email: "<EMAIL>",
      manager: "Zalak Patel",
    }
  ]

  const faqs = [
    {
      question: "How far in advance should I book your services?",
      answer:
        "We recommend booking at least 3-6 months in advance for large events, though we can accommodate shorter timelines based on availability.",
    },
    {
      question: "Do you handle international events?",
      answer:
        "Yes, we have experience managing international conferences and can coordinate with global speakers and vendors.",
    },
    {
      question: "What is included in your event management services?",
      answer:
        "Our comprehensive services include venue selection, speaker management, registration, catering, AV support, marketing, and on-site coordination.",
    },
    {
      question: "Can you work within our budget constraints?",
      answer:
        "We work closely with clients to create customized solutions that deliver maximum value within your budget.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="relative pt-32 pb-20 bg-gradient-to-br from-orange-50 via-rose-50 to-amber-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-orange-600 border border-orange-200 mb-6">
              <MessageCircle className="w-4 h-4" />
              Contact Us
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Let's Create Something{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                Extraordinary
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Ready to transform your vision into an unforgettable event experience? Our team of experts is here to
              guide you through every step of the process. Get in touch today to start planning your next successful
              event.
            </p>
          </div>

          {/* Quick Contact Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { icon: Phone, label: "24/7 Support", value: "Available" },
              { icon: Mail, label: "Response Time", value: "< 2 Hours" },
              { icon: Globe, label: "Cities Served", value: "25+" },
              { icon: Award, label: "Client Satisfaction", value: "98%" },
            ].map((stat, index) => (
              <div key={index} className="text-center bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                <stat.icon className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                <div className="text-gray-600 text-sm font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Get In{" "}
              <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Touch</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Multiple ways to reach us. Choose the method that works best for you and we'll respond promptly.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <Card
                key={index}
                className="group hover:shadow-xl transition-all duration-300 bg-white border-0 cursor-pointer overflow-hidden"
                onClick={() => info.action && window.open(info.action)}
              >
                <CardContent className="p-8 text-center relative">
                  <div
                    className={`w-16 h-16 bg-gradient-to-r ${info.color} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <info.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{info.title}</h3>
                  {info.details.map((detail, i) => (
                    <p key={i} className="text-gray-600 mb-2 leading-relaxed">
                      {detail}
                    </p>
                  ))}
                  {info.action && (
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-rose-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Contact Form */}
          <div className="grid lg:grid-cols-2 gap-16">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Send Us a Message</h2>
              <p className="text-lg text-gray-600 mb-8">
                Fill out the form below with your event requirements and we'll get back to you within 24 hours with a
                detailed proposal and next steps.
              </p>

              <Card className="bg-white shadow-2xl border-0 rounded-3xl overflow-hidden">
                <CardContent className="p-10">
                  <form className="space-y-6">
                    <div className="grid sm:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                        <Input
                          placeholder="Your full name"
                          className="border-gray-200 focus:border-orange-400 focus:ring-orange-400 h-12"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Company/Organization</label>
                        <Input
                          placeholder="Your company name"
                          className="border-gray-200 focus:border-orange-400 focus:ring-orange-400 h-12"
                        />
                      </div>
                    </div>

                    <div className="grid sm:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          className="border-gray-200 focus:border-orange-400 focus:ring-orange-400 h-12"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                        <Input
                          type="tel"
                          placeholder="+91 XXXXX XXXXX"
                          className="border-gray-200 focus:border-orange-400 focus:ring-orange-400 h-12"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid sm:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Event Type *</label>
                        <select className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:border-orange-400 focus:ring-orange-400 h-12">
                          <option value="">Select event type</option>
                          <option value="conference">Conference</option>
                          <option value="exhibition">Exhibition</option>
                          <option value="corporate">Corporate Event</option>
                          <option value="workshop">Workshop</option>
                          <option value="product-launch">Product Launch</option>
                          <option value="seminar">Seminar</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Expected Attendees</label>
                        <select className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:border-orange-400 focus:ring-orange-400 h-12">
                          <option value="">Select range</option>
                          <option value="50-100">50-100</option>
                          <option value="100-250">100-250</option>
                          <option value="250-500">250-500</option>
                          <option value="500-1000">500-1000</option>
                          <option value="1000+">1000+</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid sm:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Location</label>
                        <Input
                          placeholder="City or venue preference"
                          className="border-gray-200 focus:border-orange-400 focus:ring-orange-400 h-12"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                        <select className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:border-orange-400 focus:ring-orange-400 h-12">
                          <option value="">Select budget</option>
                          <option value="1-5L">₹1-5 Lakhs</option>
                          <option value="5-10L">₹5-10 Lakhs</option>
                          <option value="10-25L">₹10-25 Lakhs</option>
                          <option value="25-50L">₹25-50 Lakhs</option>
                          <option value="50L+">₹50+ Lakhs</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Event Timeline</label>
                      <div className="grid sm:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Preferred Start Date</label>
                          <Input
                            type="date"
                            className="border-gray-200 focus:border-orange-400 focus:ring-orange-400 h-12"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Event Duration</label>
                          <select className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:border-orange-400 focus:ring-orange-400 h-12">
                            <option value="">Select duration</option>
                            <option value="half-day">Half Day</option>
                            <option value="full-day">Full Day</option>
                            <option value="2-days">2 Days</option>
                            <option value="3-days">3 Days</option>
                            <option value="4-days">4+ Days</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Event Details & Requirements *
                      </label>
                      <Textarea
                        placeholder="Please describe your event objectives, target audience, specific requirements, themes, special arrangements, or any other details that would help us provide the best solution..."
                        rows={6}
                        className="border-gray-200 focus:border-orange-400 focus:ring-orange-400"
                        required
                      />
                    </div>

                    <div className="flex items-start gap-3">
                      <input type="checkbox" className="w-5 h-5 text-orange-600 rounded mt-0.5" required />
                      <label className="text-sm text-gray-600">
                        I agree to the{" "}
                        <a href="#" className="text-orange-600 hover:underline">
                          Terms of Service
                        </a>{" "}
                        and
                        <a href="#" className="text-orange-600 hover:underline ml-1">
                          Privacy Policy
                        </a>
                      </label>
                    </div>

                    <Button className="w-full bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white py-4 rounded-full transition-all duration-300 transform hover:scale-105 text-lg font-semibold">
                      <Send className="mr-2 w-5 h-5" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Office Locations & Additional Info */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Office Location</h2>
                <p className="text-lg text-gray-600 mb-8">
                  Visit us at any of our office locations or schedule a meeting to discuss your event requirements in
                  person.
                </p>

                <div className="space-y-6">
                  {offices.map((office, index) => (
                    <Card
                      key={index}
                      className="group hover:shadow-lg transition-all duration-300 bg-white border border-gray-100"
                    >
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <h3 className="text-xl font-semibold text-gray-900">{office.city} Office</h3>
                          <div className="text-sm bg-orange-100 text-orange-700 px-3 py-1 rounded-full">
                            {office.specialization}
                          </div>
                        </div>

                        <div className="space-y-3 text-gray-600">
                          <div className="flex items-start gap-3">
                            <MapPin className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                            <span className="leading-relaxed">{office.address}</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <Phone className="w-5 h-5 text-orange-500 flex-shrink-0" />
                            <a href={`tel:${office.phone}`} className="hover:text-orange-600 transition-colors">
                              {office.phone}
                            </a>
                          </div>
                          <div className="flex items-center gap-3">
                            <Mail className="w-5 h-5 text-orange-500 flex-shrink-0" />
                            <a href={`mailto:${office.email}`} className="hover:text-orange-600 transition-colors">
                              {office.email}
                            </a>
                          </div>
                          <div className="flex items-center gap-3">
                            <Award className="w-5 h-5 text-orange-500 flex-shrink-0" />
                            <span>Manager: {office.manager}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* FAQ Section */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h3>
                <div className="space-y-4">
                  {faqs.map((faq, index) => (
                    <Card key={index} className="bg-gradient-to-r from-orange-50 to-rose-50 border-0">
                      <CardContent className="p-6">
                        <h4 className="font-semibold text-gray-900 mb-2">{faq.question}</h4>
                        <p className="text-gray-600 text-sm leading-relaxed">{faq.answer}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Emergency Contact */}
              <Card className="bg-gradient-to-r from-orange-500 to-rose-500 border-0">
                <CardContent className="p-8 text-center text-white">
                  <h3 className="text-2xl font-bold mb-4">Need Immediate Assistance?</h3>
                  <p className="mb-6 opacity-90">For urgent inquiries or immediate support, call our 24/7 helpline.</p>
                  <Button
                    size="lg"
                    className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
                  >
                    <Phone className="mr-2 w-5 h-5" />
                    Call Now: +91 8000622067
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
