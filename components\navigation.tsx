"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, Phone, Mail, MessageCircle } from "lucide-react"
import Link from "next/link"

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  const navItems = [
    { name: "HOME", href: "/", id: "home" },
    { name: "ABOUT", href: "/about", id: "about" },
    { name: "SERVICES", href: "/services", id: "services" },
    { name: "PROJECTS", href: "/projects", id: "projects" },
    { name: "OUR WORK", href: "/our-work", id: "work" },
    { name: "DOWNLOAD", href: "/#", id: "download" },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <>
      {/* Sticky Top Contact Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-orange-600 to-rose-600 text-white py-2 px-4 text-sm">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span> <a href="tel:+918000622067">+91 8000622067</a> </span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span> <a href="mailto:<EMAIL>"><EMAIL></a> </span>
            </div>
          </div>
          <div className="hidden md:block">
            <span>Professional Conference Organizer</span>
          </div>
        </div>
      </div>

      {/* Sticky Main Navigation */}
      <nav className="fixed top-9 left-0 right-0 z-40 bg-white/95 backdrop-blur-lg shadow-xl border-b border-orange-100">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-white font-bold text-xl">OR</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">
                  OrangeRose
                </h1>
                <p className="text-xs text-gray-500 -mt-1">Event Management</p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="relative px-4 py-2 font-semibold text-sm tracking-wide transition-all duration-300 group text-gray-700 hover:text-orange-600"
                >
                  {item.name}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-orange-500 to-rose-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                </Link>
              ))}
            </div>

            {/* CTA Button */}
            <div className="hidden lg:block">
              <Link href="/contact">
                <Button className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white px-6 py-2 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Let's Talk
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-orange-50 transition-colors duration-300"
            >
              {isOpen ? <X className="w-6 h-6 text-gray-700" /> : <Menu className="w-6 h-6 text-gray-700" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`lg:hidden transition-all duration-500 overflow-hidden ${
            isOpen ? "max-h-screen opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <div className="bg-white/95 backdrop-blur-lg border-t border-orange-100 max-h-[calc(100vh-120px)] overflow-y-auto">
            <div className="container mx-auto px-4 py-6">
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsOpen(false)}
                    className="text-left px-4 py-3 rounded-lg font-semibold transition-all duration-300 text-gray-700 hover:text-orange-600 hover:bg-orange-50"
                  >
                    {item.name}
                  </Link>
                ))}
                <Link href="/contact" onClick={() => setIsOpen(false)}>
                  <Button className="w-full bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white rounded-full transition-all duration-300 mt-4">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Let's Talk
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  )
}
