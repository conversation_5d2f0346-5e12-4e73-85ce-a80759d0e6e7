import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Users, Calendar, Award, Presentation, Globe, Mic } from "lucide-react"

export default function Services() {
  const services = [
    {
      title: "Conference Management",
      description:
        "End-to-end conference planning including venue selection, speaker coordination, registration management, and technical support.",
      icon: Users,
      features: ["Advisory Board Meetings", "Investigator Meetings", "MICE Programme", "CME Programs"],
    },
    {
      title: "Exhibition Management",
      description:
        "Complete exhibition services from concept to execution, ensuring maximum visitor engagement and exhibitor satisfaction.",
      icon: Calendar,
      features: ["Sales and Marketing", "Operation Management", "Visitor Promotion", "National & International"],
    },
    {
      title: "Corporate Events",
      description: "Professional corporate event management for product launches, workshops, and business gatherings.",
      icon: Award,
      features: ["International Speaker Program", "Product Launch Events", "Live Workshops", "Team Building"],
    },
    {
      title: "Speaker Management",
      description:
        "Comprehensive speaker coordination including international speaker programs and expert panel management.",
      icon: Mic,
      features: ["International Speakers", "Panel Discussions", "Keynote Coordination", "Speaker Support"],
    },
    {
      title: "Event Marketing",
      description: "Strategic marketing and promotion services to maximize event attendance and engagement.",
      icon: Globe,
      features: ["Digital Marketing", "Social Media Promotion", "Registration Campaigns", "Brand Partnerships"],
    },
    {
      title: "Technical Support",
      description: "Complete technical infrastructure management for seamless event execution.",
      icon: Presentation,
      features: ["AV Equipment", "Live Streaming", "Registration Systems", "Event Apps"],
    },
  ]

  return (
    <section id="services" className="py-12 sm:py-24 bg-gradient-to-br from-orange-50 to-rose-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our{" "}
            <span className="bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent">Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive event management solutions tailored to your specific needs. From intimate corporate meetings
            to large-scale international conferences, we deliver excellence at every level.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card
              key={index}
              className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 bg-white/90 backdrop-blur-sm border-0 animate-fade-in-up h-full"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-6 sm:p-8 h-full flex flex-col">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-rose-500 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <service.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{service.title}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>
                <ul className="space-y-2 mb-6 flex-grow">
                  {service.features.map((feature, i) => (
                    <li key={i} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
                <Button className="w-full bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white rounded-full transition-all duration-300 mt-auto">
                  Learn More
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
